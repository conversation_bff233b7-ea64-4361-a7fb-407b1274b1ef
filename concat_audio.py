import os
import subprocess
import argparse
import sys
import re

def find_ffmpeg():
    """检查 ffmpeg 是否在 PATH 中"""
    ffmpeg_path = None
    paths = os.environ['PATH'].split(os.pathsep)
    for path in paths:
        potential_path = os.path.join(path, 'ffmpeg.exe' if sys.platform == 'win32' else 'ffmpeg')
        if os.path.isfile(potential_path) and os.access(potential_path, os.X_OK):
            ffmpeg_path = potential_path
            break

    # 尝试直接调用，以防万一它在PATH中但上面的检查失败
    if not ffmpeg_path:
        try:
            subprocess.run(['ffmpeg', '-version'], check=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            ffmpeg_path = 'ffmpeg' # 如果能直接运行，就用 'ffmpeg'
        except (subprocess.CalledProcessError, FileNotFoundError):
            pass # ffmpeg 不可用

    return ffmpeg_path

def natural_sort_key(s):
    """为自然排序生成键。"""
    # 使用 re.split 分割字符串中的数字和非数字部分
    return [int(text) if text.isdigit() else text.lower()
            for text in re.split(r'(\d+)', s)]

def concat_audio(folder_path):
    """扫描文件夹，排序 MP3 文件，并使用 ffmpeg 连接它们。"""
    ffmpeg_executable = find_ffmpeg()
    if not ffmpeg_executable:
        print("错误：找不到 FFmpeg。请确保已安装 FFmpeg 并将其添加到系统 PATH 中。", file=sys.stderr)
        sys.exit(1)

    # 将输入路径转换为绝对路径
    folder_path = os.path.abspath(folder_path)

    if not os.path.isdir(folder_path):
        print(f"错误：指定的路径 '{folder_path}' 不是一个有效的文件夹。", file=sys.stderr)
        sys.exit(1)

    mp3_files = []
    folder_name = os.path.basename(folder_path) # 现在基于绝对路径获取名称
    output_filename = f"{folder_name}.mp3"
    output_filepath = os.path.join(folder_path, output_filename) # 输出路径也是绝对的

    for filename in os.listdir(folder_path):
        if filename.lower().endswith('.mp3'):
            # 排除可能的输出文件自身 (使用绝对路径比较)
            current_file_path = os.path.join(folder_path, filename)
            if current_file_path != output_filepath:
                 # 存储绝对路径
                 mp3_files.append(current_file_path)

    if not mp3_files:
        print(f"在文件夹 '{folder_path}' 中没有找到 MP3 文件。", file=sys.stderr)
        sys.exit(0)

    # 按文件名进行自然排序
    mp3_files.sort(key=lambda x: natural_sort_key(os.path.basename(x)))
    print("\n连接顺序:")
    for file in mp3_files:
        print(f"  {os.path.basename(file)}")
    

    # 获取文件夹名称作为输出文件名
    # folder_name = os.path.basename(os.path.normpath(folder_path)) # 已移到前面
    # output_filename = f"{folder_name}.mp3"
    # output_filepath = os.path.join(folder_path, output_filename)

    # 创建供 ffmpeg 使用的列表文件 (使用绝对路径)
    # 列表文件的路径也是绝对的
    list_filename = os.path.join(folder_path, "_ffmpeg_list.txt")
    try:
        with open(list_filename, 'w', encoding='utf-8') as f:
            for mp3_file_abs_path in mp3_files: # 现在变量名表明是绝对路径
                # FFmpeg 需要对特殊字符进行转义，但通常使用绝对路径和 -safe 0 可以避免很多问题
                # 确保路径分隔符是 ffmpeg 能理解的 (通常 / 即可)
                # 写入绝对路径到列表文件
                safe_path = mp3_file_abs_path.replace("'", "'\\\''") # 基本的单引号转义
                f.write(f"file '{safe_path}'\n")

        print(f"找到 {len(mp3_files)} 个 MP3 文件，将连接为 '{os.path.basename(output_filepath)}'...")

        # 构建 ffmpeg 命令
        # 使用 -safe 0 允许非标准的文件路径，但绝对路径更可靠
        # 使用 -c copy 来避免重新编码，速度快且无损
        # 使用 -y 覆盖已存在的输出文件
        # 构建 ffmpeg 命令 (输入和输出路径现在是绝对的)
        command = [
            ffmpeg_executable,
            '-y',                 # 覆盖输出文件
            '-f', 'concat',
            '-safe', '0',         # 允许非安全路径(虽然我们用了绝对路径)
            '-i', list_filename,  # 列表文件路径也是绝对的
            '-c', 'copy',
            output_filepath       # 输出文件路径也是绝对的
        ]

        # 执行 ffmpeg 命令
        print(f"正在执行命令: {' '.join(command)}")
        process = subprocess.run(command, check=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True, encoding='utf-8')
        print("FFmpeg 输出:")
        print(process.stdout)
        if process.stderr:
            print("FFmpeg 错误输出:")
            print(process.stderr)

        print(f"\n成功！合并后的文件保存在: {output_filepath}")

    except subprocess.CalledProcessError as e:
        print("\n错误：FFmpeg 执行失败。", file=sys.stderr)
        print(f"命令: {' '.join(e.cmd)}", file=sys.stderr)
        print(f"返回码: {e.returncode}", file=sys.stderr)
        print(f"错误输出:\n{e.stderr}", file=sys.stderr)
        sys.exit(1)
    except Exception as e:
        print(f"\n发生意外错误: {e}", file=sys.stderr)
        sys.exit(1)
    finally:
        # 清理临时列表文件
        if os.path.exists(list_filename):
            try:
                os.remove(list_filename)
                print(f"已删除临时文件: {list_filename}")
            except OSError as e:
                print(f"警告：无法删除临时文件 '{list_filename}': {e}", file=sys.stderr)

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='按文件名顺序连接指定文件夹下的所有 MP3 文件。')
    parser.add_argument('folder_path', type=str, help='包含 MP3 文件的文件夹路径 A。')

    args = parser.parse_args()

    concat_audio(args.folder_path)
